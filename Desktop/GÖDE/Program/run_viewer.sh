#!/bin/bash

echo "DWG/DXF Viewer Program"
echo "======================"
echo ""

echo "Válasszon egy opciót:"
echo "1. <PERSON><PERSON><PERSON>er (DWG + <PERSON>X<PERSON>, ODA File Converter szükséges)"
echo "2. Egyszerű DXF Viewer (csak DXF fájlok)"
echo "3. Teszt DXF fájl létrehozása"
echo "4. Függőségek telepítése"
echo "5. Kilé<PERSON><PERSON>"
echo ""

read -p "Adja meg a választását (1-5): " choice

case $choice in
    1)
        echo ""
        echo "DWG Viewer indítása..."
        python3 dwg_viewer.py
        ;;
    2)
        echo ""
        echo "DXF Viewer indítása..."
        python3 dxf_viewer_simple.py
        ;;
    3)
        echo ""
        echo "Teszt DXF fájl létrehozása..."
        python3 create_test_dxf.py
        read -p "Nyomjon Enter-t a folytatáshoz..."
        ;;
    4)
        echo ""
        echo "Függőségek telepítése..."
        python3 install_dependencies.py
        read -p "Nyomjon Enter-t a folytatáshoz..."
        ;;
    5)
        echo "Kilépés..."
        exit 0
        ;;
    *)
        echo "Érvénytelen választás!"
        read -p "Nyomjon Enter-t a folytatáshoz..."
        ;;
esac
