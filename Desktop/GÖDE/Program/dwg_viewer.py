#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DWG Fájl Megjelenítő Program
Zárt alakzatok megjelenítése területszámítással
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.patches import Polygon, Circle
import numpy as np
import os
import sys

try:
    import ezdxf
    from ezdxf.addons import odafc
except ImportError:
    print("Hiányzó könyvtár: ezdxf")
    print("Telepítés: pip install ezdxf")
    sys.exit(1)

try:
    from shapely.geometry import Polygon as ShapelyPolygon, Point
    from shapely.ops import unary_union
except ImportError:
    print("Hiányzó könyvtár: shapely")
    print("Telepítés: pip install shapely")
    sys.exit(1)


class DWGViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("DWG Fájl Megjelenítő - Zárt Alakzatok")
        self.root.geometry("1200x800")
        
        # Változók
        self.current_doc = None
        self.closed_shapes = []
        self.colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
        
        self.setup_ui()
    
    def setup_ui(self):
        """Felhasználói felület beállítása"""
        # Főkeret
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Vezérlő panel
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Fájl tallózó gomb
        self.browse_btn = ttk.Button(control_frame, text="DWG Fájl Megnyitása", 
                                   command=self.browse_file)
        self.browse_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Fájl név megjelenítése
        self.file_label = ttk.Label(control_frame, text="Nincs fájl kiválasztva")
        self.file_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Matplotlib figura és canvas
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        self.canvas = FigureCanvasTkAgg(self.fig, main_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Állapotsor
        self.status_var = tk.StringVar()
        self.status_var.set("Kész")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def browse_file(self):
        """Fájl tallózó dialógus"""
        file_path = filedialog.askopenfilename(
            title="DWG fájl kiválasztása",
            filetypes=[
                ("DWG fájlok", "*.dwg"),
                ("DXF fájlok", "*.dxf"),
                ("Minden fájl", "*.*")
            ]
        )
        
        if file_path:
            self.load_dwg_file(file_path)
    
    def load_dwg_file(self, file_path):
        """DWG fájl betöltése"""
        try:
            self.status_var.set("Fájl betöltése...")
            self.root.update()
            
            # Fájl név megjelenítése
            filename = os.path.basename(file_path)
            self.file_label.config(text=f"Fájl: {filename}")
            
            # DWG fájl betöltése
            if file_path.lower().endswith('.dwg'):
                # DWG fájl konvertálása DXF-re
                try:
                    self.current_doc = odafc.readfile(file_path)
                except Exception as e:
                    messagebox.showerror("Hiba", 
                        f"DWG fájl betöltése sikertelen.\n"
                        f"Győződjön meg róla, hogy az ODA File Converter telepítve van.\n"
                        f"Hiba: {str(e)}")
                    return
            else:
                # DXF fájl közvetlen betöltése
                self.current_doc = ezdxf.readfile(file_path)
            
            # Zárt alakzatok keresése és megjelenítése
            self.find_closed_shapes()
            self.display_shapes()
            
            self.status_var.set(f"Betöltve: {len(self.closed_shapes)} zárt alakzat")
            
        except Exception as e:
            messagebox.showerror("Hiba", f"Fájl betöltési hiba: {str(e)}")
            self.status_var.set("Hiba történt")
    
    def find_closed_shapes(self):
        """Zárt alakzatok keresése a DXF dokumentumban"""
        self.closed_shapes = []
        
        if not self.current_doc:
            return
        
        modelspace = self.current_doc.modelspace()
        
        for entity in modelspace:
            shape_data = None
            
            # Kör
            if entity.dxftype() == 'CIRCLE':
                center = (entity.dxf.center.x, entity.dxf.center.y)
                radius = entity.dxf.radius
                area = np.pi * radius * radius
                shape_data = {
                    'type': 'circle',
                    'center': center,
                    'radius': radius,
                    'area': area,
                    'entity': entity
                }
            
            # Zárt poliline
            elif entity.dxftype() == 'LWPOLYLINE' and entity.closed:
                points = [(p[0], p[1]) for p in entity.get_points()]
                if len(points) >= 3:
                    try:
                        poly = ShapelyPolygon(points)
                        if poly.is_valid:
                            area = poly.area
                            centroid = poly.centroid
                            shape_data = {
                                'type': 'polygon',
                                'points': points,
                                'area': abs(area),
                                'centroid': (centroid.x, centroid.y),
                                'entity': entity
                            }
                    except Exception:
                        pass
            
            # Poligon
            elif entity.dxftype() == 'POLYLINE' and entity.is_closed:
                points = [(vertex.dxf.location.x, vertex.dxf.location.y) 
                         for vertex in entity.vertices]
                if len(points) >= 3:
                    try:
                        poly = ShapelyPolygon(points)
                        if poly.is_valid:
                            area = poly.area
                            centroid = poly.centroid
                            shape_data = {
                                'type': 'polygon',
                                'points': points,
                                'area': abs(area),
                                'centroid': (centroid.x, centroid.y),
                                'entity': entity
                            }
                    except Exception:
                        pass
            
            # Ellipszis
            elif entity.dxftype() == 'ELLIPSE':
                # Egyszerűsített ellipszis kezelés
                center = (entity.dxf.center.x, entity.dxf.center.y)
                major_axis = entity.dxf.major_axis
                ratio = entity.dxf.ratio
                
                # Ellipszis területe: π * a * b
                a = np.sqrt(major_axis.x**2 + major_axis.y**2)
                b = a * ratio
                area = np.pi * a * b
                
                shape_data = {
                    'type': 'ellipse',
                    'center': center,
                    'major_axis': major_axis,
                    'ratio': ratio,
                    'area': area,
                    'entity': entity
                }
            
            if shape_data:
                self.closed_shapes.append(shape_data)
    
    def display_shapes(self):
        """Alakzatok megjelenítése"""
        self.ax.clear()
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_title("Zárt Alakzatok Területekkel")
        
        for i, shape in enumerate(self.closed_shapes):
            color = self.colors[i % len(self.colors)]
            
            if shape['type'] == 'circle':
                # Kör rajzolása
                circle = Circle(shape['center'], shape['radius'], 
                              fill=False, edgecolor=color, linewidth=2)
                self.ax.add_patch(circle)
                
                # Terület kiírása a középpontba
                area_text = f"{shape['area']:.2f}"
                self.ax.text(shape['center'][0], shape['center'][1], area_text,
                           ha='center', va='center', fontsize=10, 
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
            
            elif shape['type'] == 'polygon':
                # Poligon rajzolása
                polygon = Polygon(shape['points'], fill=False, edgecolor=color, linewidth=2)
                self.ax.add_patch(polygon)
                
                # Terület kiírása a centroidba
                area_text = f"{shape['area']:.2f}"
                self.ax.text(shape['centroid'][0], shape['centroid'][1], area_text,
                           ha='center', va='center', fontsize=10,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
            
            elif shape['type'] == 'ellipse':
                # Ellipszis egyszerűsített megjelenítése körként
                circle = Circle(shape['center'], 
                              np.sqrt(shape['area'] / np.pi), 
                              fill=False, edgecolor=color, linewidth=2, linestyle='--')
                self.ax.add_patch(circle)
                
                # Terület kiírása
                area_text = f"{shape['area']:.2f}"
                self.ax.text(shape['center'][0], shape['center'][1], area_text,
                           ha='center', va='center', fontsize=10,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # Automatikus méretezés
        if self.closed_shapes:
            self.ax.autoscale()
        
        self.canvas.draw()


def main():
    """Főprogram"""
    root = tk.Tk()
    app = DWGViewer(root)
    root.mainloop()


if __name__ == "__main__":
    main()
