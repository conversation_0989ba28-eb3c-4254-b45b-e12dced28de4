#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Egyszerű DXF Fájl Megjelenítő Program
Csak DXF fájlokat olvas, ODA File Converter nélkül
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.patches import Polygon, Circle
import numpy as np
import os
import sys

try:
    import ezdxf
except ImportError:
    print("Hiányzó könyvtár: ezdxf")
    print("Telepítés: pip install ezdxf")
    sys.exit(1)

try:
    from shapely.geometry import Polygon as ShapelyPolygon
except ImportError:
    print("Hiányzó könyvtár: shapely")
    print("Telepítés: pip install shapely")
    sys.exit(1)


class SimpleDXFViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("Egyszerű DXF Megjelenítő - Zárt Alakzatok")
        self.root.geometry("1000x700")
        
        # Változók
        self.current_doc = None
        self.closed_shapes = []
        self.colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
        
        self.setup_ui()
    
    def setup_ui(self):
        """Felhasználói felület beállítása"""
        # Főkeret
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Vezérlő panel
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Fájl tallózó gomb
        self.browse_btn = ttk.Button(control_frame, text="DXF Fájl Megnyitása", 
                                   command=self.browse_file)
        self.browse_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Fájl név megjelenítése
        self.file_label = ttk.Label(control_frame, text="Nincs fájl kiválasztva")
        self.file_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Információs panel
        info_frame = ttk.Frame(control_frame)
        info_frame.pack(side=tk.RIGHT)
        
        self.info_label = ttk.Label(info_frame, text="Csak DXF fájlok támogatottak")
        self.info_label.pack()
        
        # Matplotlib figura és canvas
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, main_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Állapotsor
        self.status_var = tk.StringVar()
        self.status_var.set("Kész - Válasszon DXF fájlt")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def browse_file(self):
        """Fájl tallózó dialógus"""
        file_path = filedialog.askopenfilename(
            title="DXF fájl kiválasztása",
            filetypes=[
                ("DXF fájlok", "*.dxf"),
                ("Minden fájl", "*.*")
            ]
        )
        
        if file_path:
            if not file_path.lower().endswith('.dxf'):
                messagebox.showwarning("Figyelmeztetés", 
                    "Ez a program csak DXF fájlokat támogat.\n"
                    "DWG fájlokhoz használja a dwg_viewer.py programot.")
                return
            
            self.load_dxf_file(file_path)
    
    def load_dxf_file(self, file_path):
        """DXF fájl betöltése"""
        try:
            self.status_var.set("Fájl betöltése...")
            self.root.update()
            
            # Fájl név megjelenítése
            filename = os.path.basename(file_path)
            self.file_label.config(text=f"Fájl: {filename}")
            
            # DXF fájl betöltése
            self.current_doc = ezdxf.readfile(file_path)
            
            # Zárt alakzatok keresése és megjelenítése
            self.find_closed_shapes()
            self.display_shapes()
            
            self.status_var.set(f"Betöltve: {len(self.closed_shapes)} zárt alakzat")
            
        except Exception as e:
            messagebox.showerror("Hiba", f"Fájl betöltési hiba: {str(e)}")
            self.status_var.set("Hiba történt")
    
    def find_closed_shapes(self):
        """Zárt alakzatok keresése a DXF dokumentumban"""
        self.closed_shapes = []
        
        if not self.current_doc:
            return
        
        modelspace = self.current_doc.modelspace()
        
        for entity in modelspace:
            shape_data = None
            
            # Kör
            if entity.dxftype() == 'CIRCLE':
                center = (entity.dxf.center.x, entity.dxf.center.y)
                radius = entity.dxf.radius
                area = np.pi * radius * radius
                shape_data = {
                    'type': 'circle',
                    'center': center,
                    'radius': radius,
                    'area': area
                }
            
            # Zárt poliline
            elif entity.dxftype() == 'LWPOLYLINE' and entity.closed:
                points = [(p[0], p[1]) for p in entity.get_points()]
                if len(points) >= 3:
                    try:
                        poly = ShapelyPolygon(points)
                        if poly.is_valid and not poly.is_empty:
                            area = abs(poly.area)
                            centroid = poly.centroid
                            shape_data = {
                                'type': 'polygon',
                                'points': points,
                                'area': area,
                                'centroid': (centroid.x, centroid.y)
                            }
                    except Exception:
                        pass
            
            # Poligon
            elif entity.dxftype() == 'POLYLINE' and entity.is_closed:
                points = [(vertex.dxf.location.x, vertex.dxf.location.y) 
                         for vertex in entity.vertices]
                if len(points) >= 3:
                    try:
                        poly = ShapelyPolygon(points)
                        if poly.is_valid and not poly.is_empty:
                            area = abs(poly.area)
                            centroid = poly.centroid
                            shape_data = {
                                'type': 'polygon',
                                'points': points,
                                'area': area,
                                'centroid': (centroid.x, centroid.y)
                            }
                    except Exception:
                        pass
            
            if shape_data:
                self.closed_shapes.append(shape_data)
    
    def display_shapes(self):
        """Alakzatok megjelenítése"""
        self.ax.clear()
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_title("Zárt Alakzatok Területekkel")
        
        if not self.closed_shapes:
            self.ax.text(0.5, 0.5, "Nem találhatók zárt alakzatok", 
                        transform=self.ax.transAxes, ha='center', va='center',
                        fontsize=14, bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray'))
            self.canvas.draw()
            return
        
        for i, shape in enumerate(self.closed_shapes):
            color = self.colors[i % len(self.colors)]
            
            if shape['type'] == 'circle':
                # Kör rajzolása
                circle = Circle(shape['center'], shape['radius'], 
                              fill=False, edgecolor=color, linewidth=2)
                self.ax.add_patch(circle)
                
                # Terület kiírása a középpontba
                area_text = f"{shape['area']:.2f}"
                self.ax.text(shape['center'][0], shape['center'][1], area_text,
                           ha='center', va='center', fontsize=9, weight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
            
            elif shape['type'] == 'polygon':
                # Poligon rajzolása
                polygon = Polygon(shape['points'], fill=False, edgecolor=color, linewidth=2)
                self.ax.add_patch(polygon)
                
                # Terület kiírása a centroidba
                area_text = f"{shape['area']:.2f}"
                self.ax.text(shape['centroid'][0], shape['centroid'][1], area_text,
                           ha='center', va='center', fontsize=9, weight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
        
        # Automatikus méretezés
        self.ax.autoscale()
        
        # Tengelyek címkézése
        self.ax.set_xlabel("X koordináta")
        self.ax.set_ylabel("Y koordináta")
        
        self.canvas.draw()


def main():
    """Főprogram"""
    root = tk.Tk()
    app = SimpleDXFViewer(root)
    root.mainloop()


if __name__ == "__main__":
    main()
