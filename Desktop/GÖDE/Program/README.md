# DWG Fájl Megjelenítő Program

Ez a program lehetővé teszi DWG és DXF fájlok megnyitását, a benne található zárt alakzatok megjelenítését különböz<PERSON> színekkel, és a területük kiszámítását.

## Funkciók

- **DWG/DXF fájlok betöltése**: Tallózással kiválasztható fájlok
- **Zárt alakzatok felismerése**: Körök, poligonok, ellipszisek
- **Színes megjelenítés**: Minden alakzat különböző színnel
- **Területszámítás**: Automatikus területszámítás és megjelenítés
- **Grafikus felület**: Tkinter alapú, felhasználóbarát interface

## Telepítés

### 1. Függőségek telepítése

Automatikus telepítés:
```bash
python install_dependencies.py
```

Vagy manuális telepítés:
```bash
pip install -r requirements.txt
```

### 2. ODA File Converter (DWG fájlokhoz)

DWG fájlok olvasásához szükséges az ODA File Converter telepítése:

1. Látogasson el: https://www.opendesign.com/guestfiles/oda_file_converter
2. Töltse le az operációs rendszerének megfelelő verziót
3. Telepítse a programot
4. Győződjön meg róla, hogy a PATH környezeti változóban szerepel

**Megjegyzés**: DXF fájlok közvetlenül olvashatók, ODA File Converter nélkül is.

## Használat

### Program indítása
```bash
python dwg_viewer.py
```

### Lépések:
1. Kattintson a "DWG Fájl Megnyitása" gombra
2. Válasszon ki egy DWG vagy DXF fájlt
3. A program automatikusan betölti és megjeleníti a zárt alakzatokat
4. Minden alakzat különböző színnel jelenik meg
5. A területek a alakzatok közepében láthatók

## Támogatott alakzatok

- **Körök (CIRCLE)**: Teljes körök területszámítással
- **Zárt poliline-ok (LWPOLYLINE)**: Zárt poligonok
- **Zárt poligonok (POLYLINE)**: Hagyományos poligonok  
- **Ellipszisek (ELLIPSE)**: Elliptikus alakzatok

## Rendszerkövetelmények

- Python 3.7 vagy újabb
- Windows, macOS vagy Linux
- Legalább 4GB RAM
- Grafikus felület támogatás

## Hibaelhárítás

### "ODA File Converter failed" hiba
- Telepítse az ODA File Converter-t
- Ellenőrizze, hogy a PATH-ban szerepel-e
- Próbáljon DXF fájlt használni helyette

### "Hiányzó könyvtár" hiba
- Futtassa: `python install_dependencies.py`
- Vagy: `pip install -r requirements.txt`

### Üres megjelenítés
- Ellenőrizze, hogy a fájl tartalmaz-e zárt alakzatokat
- Próbáljon másik DWG/DXF fájlt

## Fejlesztői információk

### Használt könyvtárak:
- **ezdxf**: DXF/DWG fájlok olvasása
- **tkinter**: Grafikus felület
- **matplotlib**: Rajzolás és megjelenítés
- **shapely**: Geometriai számítások
- **numpy**: Numerikus számítások

### Fájlstruktúra:
```
Program/
├── dwg_viewer.py          # Fő program
├── install_dependencies.py # Telepítő script
├── requirements.txt       # Függőségek listája
└── README.md             # Dokumentáció
```

## Licenc

Ez a program oktatási és személyes használatra készült.

## Támogatás

Ha problémába ütközik, ellenőrizze:
1. Python verzió (3.7+)
2. Függőségek telepítése
3. ODA File Converter telepítése (DWG fájlokhoz)
4. Fájl formátum (DWG/DXF)

---

**Készítette**: DWG Viewer Program
**Verzió**: 1.0
**Dátum**: 2025
