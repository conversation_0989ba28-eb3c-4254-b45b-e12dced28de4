@echo off
echo DWG/DXF Viewer Program
echo ======================

echo.
echo Valasszon egy opciót:
echo 1. <PERSON><PERSON>s DWG Viewer (DWG + DXF támogatás, ODA File Converter szükséges)
echo 2. Egyszerű DXF Viewer (csak DXF fájlok)
echo 3. Teszt DXF fájl létrehozása
echo 4. Függőségek telepítése
echo 5. Kilépés

echo.
set /p choice="Adja meg a választását (1-5): "

if "%choice%"=="1" (
    echo.
    echo DWG Viewer indítása...
    python dwg_viewer.py
) else if "%choice%"=="2" (
    echo.
    echo DXF Viewer indítása...
    python dxf_viewer_simple.py
) else if "%choice%"=="3" (
    echo.
    echo Teszt DXF fájl létrehozása...
    python create_test_dxf.py
    pause
) else if "%choice%"=="4" (
    echo.
    echo Függőségek telepítése...
    python install_dependencies.py
    pause
) else if "%choice%"=="5" (
    echo Kilépés...
    exit
) else (
    echo Érvénytelen választás!
    pause
)

pause
