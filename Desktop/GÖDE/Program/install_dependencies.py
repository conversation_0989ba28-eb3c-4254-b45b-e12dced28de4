#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Függőségek telepítő script
"""

import subprocess
import sys
import os

def install_package(package):
    """Csomag telepítése pip-pel"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} sikeresen telepítve")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} telepítése sikertelen")
        return False

def main():
    """Főprogram"""
    print("DWG Viewer függőségek telepítése...")
    print("=" * 40)
    
    # Szükséges csomagok
    packages = [
        "ezdxf>=1.4.0",
        "matplotlib>=3.5.0", 
        "shapely>=2.0.0",
        "numpy>=1.21.0"
    ]
    
    success_count = 0
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 40)
    print(f"Telepítés befejezve: {success_count}/{len(packages)} csomag")
    
    if success_count == len(packages):
        print("✓ Minden függőség sikeresen telepítve!")
        print("\nMost futtathatja a programot:")
        print("python dwg_viewer.py")
    else:
        print("✗ Néhány függőség telepítése sikertelen.")
        print("Próbálja meg manuálisan:")
        print("pip install -r requirements.txt")
    
    # ODA File Converter figyelmeztetés
    print("\n" + "=" * 40)
    print("FIGYELEM: DWG fájlok olvasásához szükséges az ODA File Converter!")
    print("Letöltés: https://www.opendesign.com/guestfiles/oda_file_converter")
    print("Telepítés után a program képes lesz DWG fájlokat olvasni.")
    print("DXF fájlok közvetlenül olvashatók.")

if __name__ == "__main__":
    main()
