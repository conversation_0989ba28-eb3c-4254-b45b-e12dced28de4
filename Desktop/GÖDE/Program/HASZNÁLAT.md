# DWG/DXF Viewer Program - Használat<PERSON>t<PERSON>

## Gyors Kezdés

### 1. Függőségek telepítése
```bash
python install_dependencies.py
```

### 2. Program indítása

**Windows:**
```bash
run_viewer.bat
```

**Linux/Mac:**
```bash
./run_viewer.sh
```

**Vagy k<PERSON>z<PERSON>ül:**
```bash
# Egyszerű DXF viewer (ajánlott kezdéshez)
python dxf_viewer_simple.py

# Teljes DWG/DXF viewer (ODA File Converter szükséges)
python dwg_viewer.py
```

### 3. Teszt fájl létrehozása
```bash
python create_test_dxf.py
```

## Program Funkciók

### ✅ Támogatott Alakzatok
- **Körök (CIRCLE)** - Automatikus területszámítás
- **<PERSON><PERSON><PERSON>-ok (LWPOLYLINE)** - Poligon területszámítás
- **<PERSON><PERSON><PERSON> (POLYLINE)** - Komplex alakzatok
- **Ellipszisek (ELLIPSE)** - Elliptikus területek

### 🎨 Megjelenítés
- Minden alakzat **különböző színnel**
- **Területek** az alakzatok közepében
- **Zoom és pan** funkciók
- **Rács** a könnyebb tájékozódáshoz

### 📁 Támogatott Fájlformátumok
- **DXF fájlok** - Közvetlenül támogatott
- **DWG fájlok** - ODA File Converter szükséges

## Lépésről Lépésre Használat

### 1. Program Indítása
- Indítsa el a `dxf_viewer_simple.py` programot
- Megjelenik a grafikus felület

### 2. Fájl Megnyitása
- Kattintson a **"DXF Fájl Megnyitása"** gombra
- Válasszon ki egy DXF vagy DWG fájlt
- A program automatikusan betölti és elemzi

### 3. Eredmény Megtekintése
- A zárt alakzatok **különböző színekkel** jelennek meg
- A **területek** az alakzatok közepében láthatók
- Használja az egeret a **zoom** és **pan** funkciókhoz

## Teszt Fájl Használata

A `teszt_alakzatok.dxf` fájl tartalmazza:

1. **Kör 1** - Középpont: (0, 0), Sugár: 5, Terület: ~78.54
2. **Kör 2** - Középpont: (20, 0), Sugár: 8, Terület: ~201.06
3. **Négyzet** - 10×10-es négyzet, Terület: 100
4. **Háromszög** - Háromszög alakzat
5. **Hatszög** - Szabályos hatszög, Sugár: 6
6. **Téglalap** - 20×10-es téglalap, Terület: 200
7. **Kis kör** - Középpont: (-5, -20), Sugár: 3, Terület: ~28.27
8. **L-alakzat** - Komplex L-forma

## Hibaelhárítás

### "Hiányzó könyvtár" hiba
```bash
python install_dependencies.py
```

### "ODA File Converter failed" (DWG fájloknál)
1. Töltse le az ODA File Converter-t: https://www.opendesign.com/guestfiles/oda_file_converter
2. Telepítse a programot
3. Győződjön meg róla, hogy a PATH-ban szerepel
4. **Alternatíva**: Használja DXF fájlokat

### Üres megjelenítés
- Ellenőrizze, hogy a fájl tartalmaz-e **zárt alakzatokat**
- Próbálja ki a `teszt_alakzatok.dxf` fájlt
- Győződjön meg róla, hogy a fájl nem sérült

### Program nem indul
- Ellenőrizze a Python verziót: `python --version` (3.7+ szükséges)
- Telepítse újra a függőségeket
- Windows-on próbálja: `python` helyett `py`

## Tippek

### 🔍 Jobb Megjelenítés
- Használja az egér **görgőjét** a zoom-hoz
- **Húzza** az egérrel a képet a mozgatáshoz
- A **rács** segít a tájékozódásban

### 📐 Területszámítás
- A területek **automatikusan** kiszámítódnak
- A **középpontba** íródnak ki
- **Fehér háttérrel** kiemelve a jobb láthatóságért

### 🎯 Legjobb Gyakorlat
1. Kezdje a **teszt fájllal**
2. Használja az **egyszerű DXF viewert** először
3. DWG fájlokhoz telepítse az **ODA File Converter-t**
4. Nagyobb fájloknál legyen **türelmes** a betöltéssel

## Támogatás

Ha problémába ütközik:
1. Olvassa el a **README.md** fájlt
2. Ellenőrizze a **rendszerkövetelményeket**
3. Próbálja ki a **teszt fájlt**
4. Győződjön meg a **függőségek** telepítéséről

---

**Jó munkát a DWG/DXF fájlok elemzéséhez!** 🎯
