#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teszt DXF fájl létrehozása
Különböző zárt alakzatokkal a program teszteléséhez
"""

import ezdxf
import math

def create_test_dxf():
    """Teszt DXF fájl létrehozása különböző alakzatokkal"""
    
    # Új DXF dokumentum létrehozása
    doc = ezdxf.new('R2010')
    msp = doc.modelspace()
    
    # 1. <PERSON><PERSON><PERSON> (középen)
    circle1 = msp.add_circle(center=(0, 0), radius=5)
    
    # 2. Nagyobb kör
    circle2 = msp.add_circle(center=(20, 0), radius=8)
    
    # 3. Négyzet (zárt poliline)
    square_points = [
        (10, 10),
        (20, 10), 
        (20, 20),
        (10, 20),
        (10, 10)  # <PERSON><PERSON><PERSON> alak<PERSON>t
    ]
    square = msp.add_lwpolyline(square_points)
    square.close()
    
    # 4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    triangle_points = [
        (-10, 10),
        (-5, 20),
        (-15, 20),
        (-10, 10)  # <PERSON><PERSON><PERSON> alakzat
    ]
    triangle = msp.add_lwpolyline(triangle_points)
    triangle.close()
    
    # 5. Hatszög
    hexagon_points = []
    center_x, center_y = -20, -10
    radius = 6
    for i in range(6):
        angle = i * math.pi / 3
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        hexagon_points.append((x, y))
    hexagon_points.append(hexagon_points[0])  # Zárt alakzat
    
    hexagon = msp.add_lwpolyline(hexagon_points)
    hexagon.close()
    
    # 6. Téglalap
    rectangle_points = [
        (5, -15),
        (25, -15),
        (25, -5),
        (5, -5),
        (5, -15)  # Zárt alakzat
    ]
    rectangle = msp.add_lwpolyline(rectangle_points)
    rectangle.close()
    
    # 7. Kis kör
    small_circle = msp.add_circle(center=(-5, -20), radius=3)
    
    # 8. Komplex alakzat (L-forma)
    l_shape_points = [
        (30, 10),
        (40, 10),
        (40, 15),
        (35, 15),
        (35, 25),
        (30, 25),
        (30, 10)  # Zárt alakzat
    ]
    l_shape = msp.add_lwpolyline(l_shape_points)
    l_shape.close()
    
    # Fájl mentése
    filename = "teszt_alakzatok.dxf"
    doc.saveas(filename)
    print(f"Teszt DXF fájl létrehozva: {filename}")
    
    # Alakzatok listája információval
    shapes_info = [
        ("Kör 1", "Középpont: (0, 0), Sugár: 5, Terület: ~78.54"),
        ("Kör 2", "Középpont: (20, 0), Sugár: 8, Terület: ~201.06"),
        ("Négyzet", "10x10-es négyzet, Terület: 100"),
        ("Háromszög", "Háromszög alakzat"),
        ("Hatszög", "Szabályos hatszög, Sugár: 6"),
        ("Téglalap", "20x10-es téglalap, Terület: 200"),
        ("Kis kör", "Középpont: (-5, -20), Sugár: 3, Terület: ~28.27"),
        ("L-alakzat", "Komplex L-forma")
    ]
    
    print("\nLétrehozott alakzatok:")
    for i, (name, info) in enumerate(shapes_info, 1):
        print(f"{i}. {name}: {info}")
    
    return filename

def main():
    """Főprogram"""
    try:
        filename = create_test_dxf()
        print(f"\n✓ Sikeres! Most futtathatja a programot és nyissa meg a '{filename}' fájlt.")
        print("\nProgram futtatása:")
        print("python dxf_viewer_simple.py")
        print("vagy")
        print("python dwg_viewer.py")
        
    except Exception as e:
        print(f"Hiba a teszt fájl létrehozásakor: {e}")

if __name__ == "__main__":
    main()
